import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Platform, StatusBar } from 'react-native';
import { useEffect, useState } from 'react';

/**
 * Custom hook to handle Android-specific safe area issues
 * Addresses the navigation overflow problem on first load and stack switches
 */
export const useAndroidSafeArea = () => {
  const insets = useSafeAreaInsets();
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    // Add a small delay to ensure safe area calculations are complete
    // This fixes the Android first-load issue
    const timer = setTimeout(() => {
      setIsReady(true);
    }, Platform.OS === 'android' ? 100 : 0);

    return () => clearTimeout(timer);
  }, []);

  // Calculate safe padding with fallbacks for Android
  const safePadding = {
    paddingTop: Platform.OS === 'android'
      ? Math.max(insets.top, StatusBar.currentHeight || 0)
      : insets.top,
    paddingBottom: Platform.OS === 'android'
      ? Math.max(insets.bottom, 0)
      : insets.bottom,
    paddingLeft: insets.left,
    paddingRight: insets.right,
  };

  // Screen wrapper style that prevents overflow
  const screenStyle = {
    flex: 1,
    ...safePadding,
  };

  // Container style for content
  const containerStyle = {
    flex: 1,
    paddingTop: Platform.OS === 'android' ? 0 : undefined,
  };

  return {
    insets,
    safePadding,
    screenStyle,
    containerStyle,
    isReady,
  };
};

export default useAndroidSafeArea;
